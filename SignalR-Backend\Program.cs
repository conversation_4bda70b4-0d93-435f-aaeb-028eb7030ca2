
using Microsoft.EntityFrameworkCore;
using SignalR_Backend.Hubs;
using SignalR_Backend.Models;

namespace SignalR_Backend
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllers();

            // Add SignalR with configuration
            builder.Services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true; // Enable detailed errors for debugging
            });

            // Configure CORS for React app and SignalR
            builder.Services.AddCors(options =>
            {
                options.AddPolicy("ReactApp", policy =>
                {
                    policy.WithOrigins(
                            "http://localhost:3000",
                            "https://localhost:3000",
                            "http://localhost:3001",
                            "http://localhost:5173", // Vite dev server
                            "https://localhost:5173",
                            "http://localhost:5174", // Vite dev server
                            "https://localhost:5174",
                            "http://localhost:5175",
                            "http://localhost:5251"  // Add backend origin
                          )
                          .AllowAnyMethod()
                          .AllowAnyHeader()
                          .AllowCredentials(); // Required for SignalR
                });
            });

            // Register EF Core with SQL Server
            builder.Services.AddDbContext<ChatDbContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

            // Configure Swagger/OpenAPI
            builder.Services.AddEndpointsApiExplorer();
            builder.Services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
                {
                    Title = "SignalR Chat API",
                    Version = "v1",
                    Description = "A SignalR-based chat application API with real-time messaging capabilities",
                    Contact = new Microsoft.OpenApi.Models.OpenApiContact
                    {
                        Name = "SignalR Chat API",
                        Email = "<EMAIL>"
                    }
                });

                // Add XML comments if available
                var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
                var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
                if (File.Exists(xmlPath))
                {
                    c.IncludeXmlComments(xmlPath);
                }
            });

            var app = builder.Build();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SignalR Chat API v1");
                    c.RoutePrefix = "swagger"; // Set Swagger UI at /swagger
                    c.DocumentTitle = "SignalR Chat API Documentation";
                    c.DefaultModelsExpandDepth(-1); // Hide models section by default
                });
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();
            app.UseAuthorization();

            // Important: Use CORS after routing but before endpoints
            app.UseCors("ReactApp");

            app.MapControllers();

            // Map SignalR hub with CORS
            app.MapHub<ChatHub>("/chatHub").RequireCors("ReactApp");

            app.Run();
        }
    }
}
